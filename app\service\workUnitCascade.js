/**
 * 工作单元级联选择器服务
 * 基于 FlatMillConstructionMaterialized 物化视图的层级结构
 * 支持企业→厂房→车间→岗位的4级级联选择和远程搜索
 */

const Service = require('egg').Service;

class WorkUnitCascadeService extends Service {

  /**
   * 获取级联选择器数据
   * @param {Object} params - 查询参数
   * @param {number} params.level - 层级 (0:企业, 1:厂房, 2:车间, 3:岗位)
   * @param {string} params.parentId - 父级ID
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 级联数据
   */
  async getCascadeData({ level, parentId, keyword, pageSize, page }) {
    switch (level) {
      case 0: // 获取企业列表
        return await this.getEnterprises({ keyword, pageSize, page });
      case 1: // 获取厂房列表
        return await this.getMills({ parentId, keyword, pageSize, page });
      case 2: // 获取车间列表
        return await this.getWorkshops({ parentId, keyword, pageSize, page });
      case 3: // 获取岗位列表
        return await this.getStations({ parentId, keyword, pageSize, page });
      default:
        return [];
    }
  }

  /**
   * 获取企业列表
   * @param {Object} params - 查询参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 企业列表
   */
  async getEnterprises({ keyword, pageSize, page }) {
    const { ctx } = this;

    // 获取权限范围的企业ID
    const scopeEnterpriseIds = await ctx.helper.getScopeData('enterprise_ids') || [];
    console.log('用户权限范围的企业ID:', scopeEnterpriseIds);

    // 参考 findMillConstructionLazy 的实现，直接调用该方法
    const result = await ctx.service.mill.findMillConstructionLazy({}, false);

    console.log('企业查询结果:', result);

    // 过滤出企业级别的数据
    const enterprises = result.filter(item => item.category === 'enterprises');
    console.log('过滤后的企业数据:', enterprises);

    // 应用搜索过滤
    let filteredEnterprises = enterprises;
    if (keyword) {
      filteredEnterprises = enterprises.filter(item =>
        item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pagedEnterprises = filteredEnterprises.slice(start, end);

    const mappedResult = pagedEnterprises.map(item => ({
      value: item._id,
      label: item.name,
      isLeaf: !item.hasChildren,
      category: item.category,
      count: 1,
    }));

    console.log('最终返回的企业数据:', mappedResult);
    return mappedResult;
  }

  /**
   * 获取厂房列表
   * @param {Object} params - 查询参数
   * @param {string} params.parentId - 企业ID
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 厂房列表
   */
  async getMills({ parentId: enterpriseId, keyword, pageSize, page }) {
    const { ctx } = this;

    // 使用 findMillConstructionLazy 获取子节点
    const result = await ctx.service.mill.findMillConstructionLazy({ parentId: enterpriseId }, false);

    console.log('厂房查询结果:', result);

    // 应用搜索过滤
    let filteredMills = result;
    if (keyword) {
      filteredMills = result.filter(item =>
        item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pagedMills = filteredMills.slice(start, end);

    return pagedMills.map(item => ({
      value: item._id,
      label: item.name,
      isLeaf: !item.hasChildren,
      category: item.category,
      count: 1,
    }));
  }

  /**
   * 获取车间列表
   * @param {Object} params - 查询参数
   * @param {string} params.parentId - 父级fullId
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 车间列表
   */
  async getWorkshops({ parentId, keyword, pageSize, page }) {
    const { ctx } = this;

    // 使用 findMillConstructionLazy 获取子节点
    const result = await ctx.service.mill.findMillConstructionLazy({ parentId }, false);

    console.log('车间查询结果:', result);

    // 应用搜索过滤
    let filteredWorkshops = result;
    if (keyword) {
      filteredWorkshops = result.filter(item =>
        item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pagedWorkshops = filteredWorkshops.slice(start, end);

    return pagedWorkshops.map(item => ({
      value: item._id,
      label: item.name,
      isLeaf: !item.hasChildren,
      category: item.category,
      count: 1,
    }));
  }

  /**
   * 获取岗位列表
   * @param {Object} params - 查询参数
   * @param {string} params.parentId - 父级fullId
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 岗位列表
   */
  async getStations({ parentId, keyword, pageSize, page }) {
    const { ctx } = this;

    // 使用 findMillConstructionLazy 获取子节点
    const result = await ctx.service.mill.findMillConstructionLazy({ parentId }, false);

    console.log('岗位查询结果:', result);

    // 应用搜索过滤
    let filteredStations = result;
    if (keyword) {
      filteredStations = result.filter(item =>
        item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
      );
    }

    // 应用分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pagedStations = filteredStations.slice(start, end);

    return pagedStations.map(item => ({
      value: item._id,
      label: `${item.name}${item.workType ? ` (${item.workType})` : ''}`,
      isLeaf: !item.hasChildren,
      category: item.category,
      workType: item.workType,
      count: 1,
    }));
  }

  /**
   * 全局搜索 - 支持跨层级搜索
   * @param {Object} params - 查询参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.page - 页码
   * @returns {Array} 搜索结果
   */
  async globalSearch({ keyword, pageSize, page }) {
    const { ctx } = this;

    // 构建搜索条件
    const query = {
      state: '1', // 只搜索有效数据
      $or: [
        { enterpriseName: { $regex: keyword, $options: 'i' } },
        { name: { $regex: keyword, $options: 'i' } },
        { fullPath: { $regex: keyword, $options: 'i' } },
        { workType: { $regex: keyword, $options: 'i' } },
      ],
    };

    // 执行查询
    const results = await ctx.service.db.find('FlatMillConstructionMaterialized', query, {
      sort: { fullPath: 1, name: 1 },
      skip: (page - 1) * pageSize,
      limit: parseInt(pageSize),
    });

    // 转换为搜索结果格式
    return results.map(item => ({
      value: item.fullId,
      label: this.formatSearchLabel(item),
      level: item.level,
      fullPath: item.fullPath,
      enterpriseName: item.enterpriseName,
      millName: item.millName,
      workspaceName: item.workspaceName,
      stationName: item.stationName,
      workType: item.workType,
      employeeCount: item.totalEmployeeCount || 0,
    }));
  }

  /**
   * 格式化搜索结果标签
   * @param {Object} item - 数据项
   * @returns {string} 格式化后的标签
   */
  formatSearchLabel(item) {
    const parts = [];

    // 添加企业名称
    if (item.enterpriseName) {
      parts.push(item.enterpriseName);
    }

    // 添加完整路径
    if (item.fullPath) {
      parts.push(item.fullPath);
    }

    // 如果是岗位且有工种，添加工种信息
    if (item.level === 'stations' && item.workType) {
      parts.push(`(${item.workType})`);
    }

    // 如果有人员数量，添加人员信息
    if (item.totalEmployeeCount > 0) {
      parts.push(`[${item.totalEmployeeCount}人]`);
    }

    return parts.join(' / ');
  }
}

module.exports = WorkUnitCascadeService;
