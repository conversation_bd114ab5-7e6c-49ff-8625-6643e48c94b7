<template>
  <div style="text-align: left">
    <el-tabs type="border-card" v-model="isSelected" @tab-click="changeMouth">
      <el-tab-pane v-for="(item, i) in dateFilter" :key="i" :label="item" :name="i">
        <div class="topBar">
          <div class="left">
            <span style="font-size: 14px;">年份：</span>
            <el-date-picker
              v-model="year"
              type="year"
              clearable="false"
              placeholder="选择年份"
              size="mini"
              style="margin-right: 10px"
              @change="handleCommand"
            >
            </el-date-picker>
            <!-- <el-button type="primary" icon="el-icon-download" size="mini" @click="exportMouth" style="margin-bottom: 15px">导出月度</el-button> -->
          </div>
          <div class="right">
            <el-input
              placeholder="请输入内容"
              suffix-icon="el-icon-search"
              v-model="searchKey"
              style="width: 240px; margin-right: 10px; margin-bottom: 15px"
              @change="search(searchKey)"
              clearable
              size="mini">
            </el-input>
          </div>
        </div>
        <el-table
          :data="listData"
          ref="table"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
          stripe
        >
          <el-table-column
            :selectable="getSelectIndex"
            type="selection"
          ></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            width="60"
            align="center"
          ></el-table-column>

          <el-table-column
            v-for="item in tableHeader"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            :min-width="item.minWidth"
            align="center"
          >
            <template slot-scope="scope">
              <div v-if="item.prop === 'workspaces'">
                <div v-for="space in scope.row.workspaces">{{ space }}</div>
                <div v-if="!scope.row.workspaces || !scope.row.workspaces.length">-</div>
              </div>
              <div v-else-if="item.prop === 'products'">
                <div v-for="item in scope.row.products">
                  {{ `${item.product} × ${item.number}` }}
                </div>
              </div>
              <div v-else-if="item.prop === 'claimType' && branch==='wh'">
                <div>{{ claimTypeList.find(type => type.value === scope.row.claimType)&&claimTypeList.find(type => type.value == scope.row.claimType).name || '-' }}</div>
              </div>
              <div v-else-if="item.prop === 'maxNumber' && branch!=='wh'">
                <div>{{ scope.row.maxNumber || '-' }}</div>
              </div>
              <div v-else-if="item.prop === 'employeeInfo'">
                <div>{{ scope.row.employeeInfo.length > 0 ? scope.row.employeeInfo[0].name : '/' }}</div>
              </div>
              <div v-else-if="item.prop === 'auditStatus'">
                <!-- <span v-if="scope.row.auditStatus === 0" class="undoBtn">未审核</span>
                <span v-else-if="scope.row.auditStatus === 1" class="doBtn">已通过</span>
                <span v-else class="reBtn">已驳回</span> -->
                  <span v-if="scope.row.auditStatus === 0" class="undoBtn">未审核</span>
                  <span v-if="scope.row.auditLevel === 2 && scope.row.auditStatus === 1 && branch === 'wh'" class="firstBtn">一审通过</span>
                  <span v-if="scope.row.auditLevel === 1 && scope.row.auditStatus === 1" class="doBtn">已通过</span>
                  <span v-if="scope.row.auditLevel === 2 && scope.row.auditStatus === 3" class="doBtn">已通过</span>
                  <span v-if="scope.row.auditStatus === 2" class="reBtn">已驳回</span>
              </div>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" align="center">
            <template slot-scope="scope">
               <!-- 未审核状态，显示通过和驳回按钮（一级审批） -->
                <div v-if="scope.row.auditStatus === 0">
                  <el-button type="primary" size="mini" @click="confirm(scope.row, 1)">通过</el-button>
                  <el-button type="danger" size="mini" @click="refuse(scope.row, 1)">驳回</el-button>
                    <el-button 
                      type="primary" 
                      size="mini" 
                      @click="viewAuditHistory(scope.row)">审批历史</el-button>
                </div>
                <!-- 对于WH分支，一级审批通过后，显示二级审批按钮 -->
                <div v-if="scope.row.auditLevel === 2 && scope.row.auditStatus === 1 && branch === 'wh'">
                  <el-button type="primary" size="mini" @click="confirm(scope.row, 2)">终审通过</el-button>
                  <el-button type="danger" size="mini" @click="refuse(scope.row, 2)">终审驳回</el-button>
                    <el-button 
                      type="primary" 
                      size="mini" 
                      @click="viewAuditHistory(scope.row)">审批历史</el-button>
                </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pagebreak">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="current"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalLength"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog :title="dialogTitle||'选择发放防护用品'" :visible.sync="dialogFormVisible">
      <el-form :model="dialogForm">
        <el-form-item label="员工名称:" :label-width="formLabelWidth">
          {{ dialogForm.name }}
        </el-form-item>
        <el-form-item label="申领用品:" :label-width="formLabelWidth">
          {{ dialogForm.product }}
        </el-form-item>
        <el-form-item label="  数量:" :label-width="formLabelWidth">
          <el-input-number v-model="dialogForm.num" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="选择型号:" :label-width="formLabelWidth">
          <el-select v-model="dialogForm.type" placeholder="请选择用品型号" @change="handleSelectChange" style="width: 300px">
            <el-option v-for="item in productType" :key="item._id" :label="item.product" :value="item._id">
              <span style="float: left">{{ item.product }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.productSpec }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="uploadConfirm">确 定</el-button>
      </div>
    </el-dialog>
      <el-dialog title="审批历史" :visible.sync="historyDialogVisible" width="600px">
    <el-timeline>
      <el-timeline-item
        v-for="(record, index) in auditRecords"
        :key="index"
        :type="getTimelineItemType(record.auditStatus)"
        :color="getTimelineItemColor(record.auditStatus)"
        :timestamp="formatDate(record.auditTime)"
      >
        <div class="audit-record">
          <span class="audit-level">{{ record.auditLevel === 1 ? '一级审批' : '二级审批' }}: </span>
          <span :class="getStatusClass(record.auditStatus)">
            {{ getStatusText(record.auditStatus) }}
          </span>
          <div class="audit-operator">审批人: {{ record.operator && record.operator.name || '未知' }}</div>
          <div v-if="record.reason" class="audit-reason">意见: {{ record.reason }}</div>
        </div>
      </el-timeline-item>
    </el-timeline>
    <div slot="footer" class="dialog-footer">
      <el-button @click="historyDialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
  </div>
</template>

<script>
import { getApplicationProducts, selectApplication, deleteScrapRecord, getDefendProductList } from "@/api";
import { scrapOutputFn } from '../utils/output.js'
import moment from "moment";
import { mapState } from 'vuex'
export default {
  data() {
    return {
      year: "",
      selectedTime: 'time1',
      listData: [],
      current: 1,
      pageSize: 30,
      pageSizes: [10, 20, 30, 50],
      totalLength: 0, // 数量
      isSelected: 0, // 默认当前月份
      searchKey: "", // 关键字
      buttonShow: false, // 使用禁用批量操作键
      multipleSelection: [],
      dateFilter: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      tableHeader: [
        {
          prop: "workspaces",
          label: "工作场所",
          width: "100",
        },
        {
          prop: "products",
          label: "申领用品",
          minWidth: "120"
        },
        {
          prop: "employeeInfo",
          label: "申请人",
          width: "120"
        },
        {
          prop: "claimType",
          label: "申请类型",
        },
        {
          prop: "maxNumber",
          label: "首次发放最大允许量",
          width: "110"
        },
        {
          prop: "notes",
          label: "申请理由",
          width: "170"
        },
        {
          prop: "applicationTime",
          label: "申请时间",
          width: "150"
        },
        {
          prop: "auditStatus",
          label: "审核状态",
          width: "170"
        },
      ],
      dialogFormVisible: false,
      dialogForm: {
        name: '',
        product: '',
        num: 0,
        type: [],
      },
      formLabelWidth: '100px',
      productsType: [],
      productType: [],
      currentProduct: {},
      claimTypeList: [
					{
						name:'到期更换',
						value: '1'
					},
					{
						name: '以旧换新',
						value: '2'
					},
					{
						name: '其他', 
						value: '3'
					}
      ],
      historyDialogVisible: false,
      auditRecords: [],
      dialogTitle: '选择发放防护用品'
    };
  },
  computed: {
    ...mapState('protectionCloudManagement', ['branch'])
  },
  mounted() {
    const now = new Date();
    this.isSelected = now.getMonth();
    this.year = now.getFullYear() + "";
      console.log('这是挂载');

    this.getList();
  },

  methods: {
    // 获取数据
    async getList() {
      const params = {
        current: this.current,
        pageSize: this.pageSize,
        isSelected: this.isSelected * 1 + 1, // 当前月份
        year: this.year * 1, // 当前年
        query: {
          searchKey: this.searchKey, // 关键字
        }
      }
      const res = await getApplicationProducts(params);
      if (res.status === 200) {
        console.log(res, 'res');
        this.listData = res.data.data;
        this.totalLength = res.data.totalLength;
      }
    },

    // 改变页面显示数量
    handleSizeChange(e) {
      this.pageSize = e;
      this.getList();
    },

    // 改变当前页
    handleCurrentChange(e) {
      this.current = e;
      this.getList();
    },

    handleSelectChange(e) {
      const a = this.productType.filter(item => item._id === e);
      if (a.length === 0) return;
      this.dialogForm.modelNumber = a[0].productSpec;
      this.dialogForm.productSpec = a[0].productSpec;
    },

    // 获取防护用品
    async getDefendProductList(type){
      console.log(type, '进来了');
      if (this.productsType.length === 0) {
        const res = await getDefendProductList()
          if (res.status === 200) {
            this.productsType = res.data.list;
          }
      }
      this.productsType.forEach(product => {
        console.log(product, 'product');
        if (product._id === type) {
          this.productType = product.data;
        }
      })
      console.log(this.productsType, 'this.productsType');
      console.log(this.productType, 'this.productType');
    },

    // 确认方法
       confirm(row, auditLevel = 1) {
          // 对于二级审批，直接进行通过操作，不打开型号选择对话框
         if (this.branch === 'wh' && auditLevel === 2) {
           const actionText = '二级终审';
           this.$confirm(`是否通过${actionText}?`, '提示', {
             confirmButtonText: '确定',
             cancelButtonText: '取消',
             type: 'warning'
           }).then(async () => {
             const res = await selectApplication({
               _id: row._id,
               status: 1,
               auditLevel: auditLevel,
               employeeInfo: row.employeeInfo,
               ...row
             });
             if (res.status === 200) {
               this.getList();
               this.$message({
                 type: 'success',
                 message: res.data
               });
             }
           })
         }else{

         console.log(row, '走着来了');
          //   await this.getDefendProductList(row.products[0].productIds);
          //   const a = this.productType.filter(item => item._id === row.products[0].productSpecId);
          //   if(a.length > 0){
          //     row.products[0].modelNumber = a[0].modelNumber;
          //   }
          //   row.products[0].modelNumber = row.products[0].productSpec;
          //   row.products[0].productIds = [row.products[0].productIds, row.products[0].productSpecId]
          // const res = await selectApplication({ status: 1, ...row });
          //   if (res.status === 200) {
          //     this.getList();
          //     this.$message({
          //       type: 'success',
          //       message: '审核通过!'
          //     });
          //     return;
          //   }
          // 非WH分支保持原有逻辑
          this.getDefendProductList(row.products[0].productIds);
          this.dialogForm.name = row.employeeInfo[0].name;
          this.dialogForm.num = row.products[0].number;
          this.dialogForm.product = row.products[0].product;
          this.dialogForm.type = '';
          this.dialogForm.productSpec = '';
          this.currentProduct = row;
          // 保存审批级别，供后续使用
          this.currentProduct.auditLevel = auditLevel;
          // 对于WH分支，仅改变弹窗标题
          if (this.branch === 'wh') {
            const dialogTitle = auditLevel === 1 ? '一级审批 - 选择防护用品型号' : '二级审批 - 选择防护用品型号';
            this.dialogTitle = dialogTitle;
          } else {
            this.dialogTitle = '选择发放防护用品';
          }

          this.dialogFormVisible = true;
         }

        },

    async uploadConfirm() {
      const products = [
        {
          _id: this.currentProduct.products[0]._id,
          productIds: [ this.currentProduct.products[0].productIds, this.dialogForm.type ],
          product: this.dialogForm.product, // 产品名称
          modelNumber: this.dialogForm.modelNumber, // 型号
          productSpec: this.dialogForm.productSpec, // 型号
          number: this.dialogForm.num, // 数量
        }
      ]
      const params = this.currentProduct;
      params.products = products;
        // 获取审批级别
      const auditLevel = params.auditLevel || 1;
       const res = await selectApplication({ 
          status: 1, 
          auditLevel: auditLevel,
          ...params 
        });
        
        if (res.status === 200) {
          this.dialogFormVisible = false;
          this.$message({
            message: res.data,
            type: "success",
          });
          this.getList();
        }
    },

   // 驳回方法
    refuse(row, auditLevel = 1) {
      const promptTitle = this.branch === 'wh' && auditLevel === 2 ? '二级审批驳回理由' : '驳回理由';
      
      this.$prompt('请输入', promptTitle, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(async (value) => {
        // 加入auditLevel参数，以便后端区分
        const res = await selectApplication({ 
          _id: row._id, 
          status: 2, 
          auditLevel: auditLevel,
          reason: value,
          employeeInfo: row.employeeInfo 
        });
        if (res.status === 200) {
          this.$message({
            type: 'success',
            message: res.data,
          });
          this.getList();
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });       
      });
    },

    // 全选方法
    handleSelectionChange(val) {
      this.buttonShow = true;
      this.multipleSelection = [];
      console.log(this.multipleSelection, '22222');
      this.multipleSelection = val;
      if (this.multipleSelection.length == 0) {
        this.buttonShow = false;
      }
    },

    // 选择年份
    handleCommand(command) {
      console.log(command, '选择年份');
      this.year = moment(command).format("YYYY");
      // this.year = command;
      this.getList();
    },

    // 选择月份
    changeMouth(tab, event) {
      this.isSelected = tab.name * 1;
      this.getList()
    },

    // 搜索方法
    search() {
      this.getList();
    },

    // 导出月度记录
    async exportMouth() {
      this.$confirm(`是否导出${this.year}年${this.isSelected}月的报废记录？`, '导出记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        scrapOutputFn({
          mouthNumber: this.isSelected,
          yearNumber: this.year,
        }, this) // 传递Vue实例以便显示提示
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        });
      });
    },

     // 查看审批历史
      viewAuditHistory(row) {
        console.log(row, '走进来了');
        this.auditRecords = row.auditRecords || [];
        this.historyDialogVisible = true;
      },
      
      formatDate(date) {
        return moment(date).format("YYYY-MM-DD HH:mm:ss");
      },
      
      getTimelineItemType(auditStatus) {
        return (auditStatus === 1 || auditStatus === 3) ? 'success' : 'danger';
      },
      
      getTimelineItemColor(auditStatus) {
        return (auditStatus === 1 || auditStatus === 3) ? '#67C23A' : '#F56C6C';
      },
      
      getStatusClass(auditStatus) {
        return (auditStatus === 1 || auditStatus === 3) ? 'doBtn' : 'reBtn';
      },
      
      getStatusText(auditStatus) {
        if (auditStatus === 1) return '通过';
        if (auditStatus === 2) return '驳回';
        if (auditStatus === 3) return '通过';
        return '未审核';
      }
  },
};
</script>

<style scoped>
.topBar {
  display: flex;
  justify-content: space-between;
}

element.style {
  z-index: 2007;
}
.el-date-editor.el-input {
  width: 135px;
}
.pagebreak {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-table colgroup.gutter {
  display: table-cell !important;
}
.el-range-editor.el-input__inner {
  position: absolute;
  right: 90px;
}

.el-input__inner {
  width: 83%;
}

.box-card {
  width: 98%;
  height: 100%;
  margin: 0 auto;
}

.font-small {
  font-size: 14px;
  color: rgb(200, 208, 214);
  margin: 0 auto;
  text-align: center;
}

.corss-line {
  margin-top: 30px;
  border: 1px solid rgb(200, 208, 214);
}

.buttons {
  margin-top: 30px;
  text-align: center;
}

.uploadImage {
  width: 90%;
}

.template {
  position: relative;
  display: flex;
  /* margin-top: 20px; */
  margin-bottom: 20px;
}

.undoBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: rgba(0,0,0,0.10);
  border-radius: 4px;
  text-align: center;
  color: #aaaaaa;
  line-height: 24px;
}

.doBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: #ddf3d8;
  border-radius: 4px;
  font-weight: 400;
  text-align: center;
  color: #00cd18;
  line-height: 24px;
}

.reBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: #ffe4cb;
  border-radius: 4px;
  font-weight: 400;
  text-align: center;
  color: #f97d0b;
  line-height: 24px;
}
.firstBtn {
  display: inline-block;
  width: 60px;
  height: 24px;
  font-size: 14px;
  background: #e6f7ff;
  border-radius: 4px;
  font-weight: 400;
  text-align: center;
  color: #1890ff;
  line-height: 24px;
}
/* 添加到<style>部分 */
.audit-record {
  padding: 8px;
  border-radius: 4px;
  background-color: #f8f8f8;
}

.audit-level {
  font-weight: bold;
}

.audit-operator {
  margin-top: 8px;
  font-size: 14px;
}

.audit-reason {
  margin-top: 4px;
  font-size: 14px;
  color: #606266;
}
</style>
