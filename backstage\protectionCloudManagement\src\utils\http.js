import axios from 'axios';
import { Loading, Message } from 'element-ui';

let loadingInstance; // load加载

/**
 * axios请求拦截器
 * @param {object} config axios请求配置对象
 * @return {object} 请求成功或失败时返回的配置对象或者promise error对象
 */
axios.defaults.baseURL = process.env.BASE_URL;
axios.interceptors.request.use(config => {
  // 这里的config包含每次请求的内容
  // 判断localStorage中是否存在api_token
  // if (localStorage.getItem('api_token')) {
  //   //  存在将api_token写入 request header
  //   config.headers.apiToken = `${localStorage.getItem('api_token')}`;
  // }
  loadingInstance = Loading.service({ 
    fullscreen: true, 
    text: '数据加载中...', 
    spinner: 'el-icon-loading', 
    background: 'rgba(0, 0, 0, 0.7)' 
  }); // 请求打开loading
  return config;
}, err => {
  Message.error({
    message: '请求失败',
    onClose: function () {
      //关闭时的回调函数, 参数为被关闭的 message 实例
    }
  });
  return Promise.reject(err);
});

/**
 * axios响应拦截器
 * @param {object} response 从服务端响应的数据
 * @return {object} 返回Promise
 */
axios.interceptors.response.use(response => {
  loadingInstance.close(); // 请求完成关闭loading
  return response;
}, err => {
  loadingInstance.close(); // 请求完成关闭loading
  Message.error({
    message: '网络异常',
    onClose: function () {
      //关闭时的回调函数, 参数为被关闭的 message 实例
    }
  });
  return Promise.reject(err);
});

/**
 * 检查完整性
 * @param {object} response 从服务端响应的数据
 * @return {object} 返回Promise
 */
const checkStatus = (response) => {
  // loading
  // 如果http状态码正常，则直接返回数据
  if (response && (response.status === 200 || response.status === 304 || response.status === 400)) {
    // 如果不需要除了data之外的数据，可以直接 return response.data
    return response.data;
  }
  // 异常状态下，把错误信息返回去
  return {
    status: -404,
    msg: '网络异常'
  };
};

/**
 * 检查code值
 * @param {object} res 从服务端响应的数据
 * @return {object} 返回Promise
 */
const checkCode = (res) => {
  // 如果code异常(这里已经包括网络错误，服务器错误，后端抛出的错误)，可以弹出一个错误提示，告诉用户
  if (res.status === -404) {
    Message.error({
      message: res.msg,
      onClose: function () {
        //关闭时的回调函数, 参数为被关闭的 message 实例
      }
    });
  }
  if (res.data && res.data.code !== 200) {
    // Message.error({
    //   message: res.data.message,
    //   onClose: function () {
    //     //关闭时的回调函数, 参数为被关闭的 message 实例
    //   }
    // });
  }
  return res;
};

// 请求方式的配置
export default {
  post(url, data, config) { //  post
    return axios({
      method: 'post',
      withCredentials: true, // 重要：携带cookie信息
      baseURL: process.env.BASE_URL,
      url,
      data,
      timeout: 15000,
      config,
      // headers: {
      //   'X-Requested-With': 'XMLHttpRequest',
      //   'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      // }
    }).then(
      response => {
        return checkStatus(response);
      }
    ).then(
      res => {
        return checkCode(res);
      }
    );
  },
  get(url, params) { // get
    return axios({
      method: 'get',
      withCredentials: true, // 重要：携带cookie信息
      baseURL: process.env.BASE_URL,
      url,
      params, // get 请求时带的参数
      timeout: 5000,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
      },
    }).then(
      response => {
        return checkStatus(response);
      }
    ).then(
      res => {
        return checkCode(res);
      }
    );
  },
};
