/**
 * 工作单元级联选择器控制器
 * 基于 FlatMillConstructionMaterialized 物化视图
 * 支持企业→厂房→车间→岗位的4级级联选择和远程搜索
 */

const Controller = require('egg').Controller;

class WorkUnitCascadeController extends Controller {

  /**
   * 获取级联选择器数据 (统一接口)
   * 支持按层级获取子级数据和全局搜索
   *
   * 查询参数:
   * - level: 层级 (0:企业, 1:厂房, 2:车间, 3:岗位, -1:全局搜索)
   * - parentId: 父级ID (level > 0 时必需)
   * - keyword: 搜索关键词 (可选，全局搜索时必需)
   * - pageSize: 每页数量 (默认50)
   * - page: 页码 (默认1)
   */
  async getCascadeData() {
    const { ctx } = this;

    // 处理参数，支持直接查询参数和JSON编码的params参数
    let queryParams = ctx.request.query;
    if (queryParams.params) {
      try {
        queryParams = JSON.parse(queryParams.params);
      } catch (error) {
        console.error('解析params参数失败:', error);
      }
    }

    const { level = 0, parentId, keyword, pageSize = 50, page = 1 } = queryParams;

    console.log('级联查询参数:', { level, parentId, keyword, pageSize, page });
    console.log('用户会话信息:', ctx.session.adminUserInfo);

    try {
      const levelNum = parseInt(level);

      // 全局搜索模式
      if (levelNum === -1) {
        if (!keyword || keyword.trim().length < 2) {
          ctx.body = {
            code: 400,
            message: '全局搜索时关键词至少需要2个字符',
            data: [],
          };
          return;
        }

        const result = await ctx.service.workUnitCascade.globalSearch({
          keyword: keyword.trim(),
          pageSize: parseInt(pageSize),
          page: parseInt(page),
        });

        ctx.body = {
          code: 200,
          message: 'success',
          data: result,
          searchMode: true,
        };
        return;
      }

      // 级联查询模式
      if (levelNum < 0 || levelNum > 3) {
        ctx.body = {
          code: 400,
          message: '层级参数无效，必须在0-3之间，或-1表示全局搜索',
          data: [],
        };
        return;
      }

      if (levelNum > 0 && !parentId) {
        ctx.body = {
          code: 400,
          message: '获取子级数据时必须提供父级ID',
          data: [],
        };
        return;
      }

      const result = await ctx.service.workUnitCascade.getCascadeData({
        level: levelNum,
        parentId,
        keyword,
        pageSize: parseInt(pageSize),
        page: parseInt(page),
      });

      ctx.body = {
        code: 200,
        message: 'success',
        data: result,
        searchMode: false,
      };
    } catch (error) {
      ctx.logger.error('获取级联数据失败:', error);
      ctx.body = {
        code: 500,
        message: '获取数据失败',
        data: [],
      };
    }
  }

  /**
   * 全局搜索接口 (向后兼容)
   * 实际调用统一接口的全局搜索模式
   */
  async globalSearch() {
    const { ctx } = this;

    // 处理参数，支持直接查询参数和JSON编码的params参数
    let queryParams = ctx.request.query;
    if (queryParams.params) {
      try {
        queryParams = JSON.parse(queryParams.params);
      } catch (error) {
        console.error('解析params参数失败:', error);
      }
    }

    const { keyword, pageSize = 20, page = 1 } = queryParams;

    // 参数验证
    if (!keyword || keyword.trim().length < 2) {
      ctx.body = {
        code: 400,
        message: '搜索关键词至少需要2个字符',
        data: [],
      };
      return;
    }

    try {
      const result = await ctx.service.workUnitCascade.globalSearch({
        keyword: keyword.trim(),
        pageSize: parseInt(pageSize),
        page: parseInt(page),
      });

      ctx.body = {
        code: 200,
        message: 'success',
        data: result,
        total: result.length,
      };
    } catch (error) {
      ctx.logger.error('全局搜索失败:', error);
      ctx.body = {
        code: 500,
        message: '搜索失败',
        data: [],
      };
    }
  }
}

module.exports = WorkUnitCascadeController;
