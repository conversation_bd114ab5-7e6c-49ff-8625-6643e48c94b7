<template>
  <div class="new-protection-plan">
    <!-- 操作栏 -->
    <el-card style="margin-bottom: 20px;">
      <div class="action-bar">
        <div class="left-actions">
          <el-cascader
            v-model="selectedWorkUnit"
            :options="workUnitOptions"
            :props="cascaderPropsComputed"
            placeholder="选择工作单元"
            clearable
            filterable
            @change="handleWorkUnitChange"
            style="width: 250px; margin-right: 10px;"
          />

          <el-input
            v-model="searchKeyword"
            placeholder="搜索内容"
            style="width: 200px; margin-right: 10px;"
            clearable
          />

          <el-select v-model="selectedStatus" placeholder="配置状态" style="width: 120px; margin-right: 10px;">
            <el-option label="全部" value="" />
            <el-option label="已配置" value="configured" />
            <el-option label="未配置" value="unconfigured" />
            <el-option label="无需配置" value="no_need" />
          </el-select>

          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 查询
          </el-button>
        </div>

        <div class="right-actions">
          <el-button type="primary" @click="handleImportStandards">
            <i class="el-icon-upload2"></i> 导入标准
          </el-button>
          <!-- <el-button type="success" @click="handleAddStandards">
            <i class="el-icon-plus"></i> 新增标准
          </el-button> -->
        </div>
      </div>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table
        :data="tableData"
        :loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :header-cell-style="{background:'#F5F7FA',color:'#4C91E9', 'font-weight':'normal'}"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="enterpriseName" label="企业" width="120" show-overflow-tooltip sortable />

        <el-table-column prop="fullPath" label="工作场所" width="300" show-overflow-tooltip sortable />

        <el-table-column label="标准配置" min-width="250">
          <template slot-scope="scope">
            <div class="protection-standards-cell">
              <!-- 防护用品类别汇总 -->
              <div v-if="scope.row.protectionPlans && scope.row.protectionPlans.length > 0" class="standards-summary">
                <div class="category-summary">
                  <el-tag
                    v-for="(category, index) in getCategorySummary(scope.row.protectionPlans)"
                    :key="index"
                    size="small"
                    :type="getTagType(category.name)"
                    style="margin-right: 6px; margin-bottom: 4px;"
                  >
                    {{ category.name }} ×{{ category.count }}
                  </el-tag>
                </div>

              </div>
              <div v-else class="no-config">
                <i class="el-icon-warning" style="color: #E6A23C;"></i>
                <span style="color: #999; margin-left: 5px;">暂无配置</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="configStatus" label="配置状态" width="130" align="center">
          <template slot-scope="scope">
            <!-- 如果是无需配置状态，显示可编辑的选择器 -->
            <el-select
              v-if="scope.row.configStatus === 'no_need'"
              v-model="scope.row.configStatus"
              size="mini"
              @change="handleStatusChange(scope.row)"
              style="width: 100%;"
            >
              <el-option label="已配置" value="configured" disabled>
                <span style="color: #67C23A;">
                  <i class="el-icon-check"></i> 已配置
                </span>
              </el-option>
              <el-option label="未配置" value="unconfigured" disabled>
                <span style="color: #E6A23C;">
                  <i class="el-icon-warning"></i> 未配置
                </span>
              </el-option>
              <el-option label="无需配置" value="no_need">
                <span style="color: #909399;">
                  <i class="el-icon-minus"></i> 无需配置
                </span>
              </el-option>
            </el-select>

            <!-- 其他状态显示为标签，但可以改为无需配置 -->
            <div v-else style="display: flex; align-items: center; justify-content: space-between;">
              <el-tag
                :type="getStatusTagType(scope.row.configStatus)"
                size="small"
                style="flex: 1;"
              >
                <i :class="getStatusIcon(scope.row.configStatus)"></i>
                {{ getStatusText(scope.row.configStatus) }}
              </el-tag>
              <el-dropdown
                @command="(command) => handleStatusCommand(command, scope.row)"
                trigger="click"
                size="mini"
                style="margin-left: 4px;"
              >
                <el-button type="text" size="mini" style="padding: 0; margin-left: 4px;">
                  <i class="el-icon-more" style="color: #909399;"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="no_need">
                    <i class="el-icon-minus"></i> 设为无需配置
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="handleConfig(scope.row)"
            >
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="showStandardDetail(scope.row)"
              v-if="scope.row.protectionPlans && scope.row.protectionPlans.length > 0"
            >
              <!-- <i class="el-icon-view"></i> 查看 -->
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>
    
    <!-- 配置弹窗 -->
    <config-dialog 
      ref="configDialog" 
      @save="handleConfigSave"
    />
    
    <!-- 员工列表弹窗 -->
    <employee-list-dialog 
      ref="employeeDialog"
    />
    
    <!-- 标准详情弹窗 -->
    <standard-detail-dialog
      ref="standardDialog"
    />

    <!-- 新增标准弹窗 -->
    <new-standard-dialog
      ref="newStandardDialog"
      @save="handleNewStandardSave"
    />

    <!-- 配发标准导入弹窗 -->
    <standard-import-dialog
      :visible.sync="standardImportDialogVisible"
      @import-success="handleImportSuccess"
    />
  </div>
</template>

<script>
import ConfigDialog from './ConfigDialog.vue';
import EmployeeListDialog from './EmployeeListDialog.vue';
import StandardDetailDialog from './StandardDetailDialog.vue';
import StandardImportDialog from './StandardImportDialog.vue';
import { getFlatStandardsList, updateConfigStatus, findMillConstruction } from '@/api/index';
import { getProtectionCategoryTree } from '@/api/protectionCategory';

export default {
  name: 'NewProtectionPlan',

  components: {
    ConfigDialog,
    EmployeeListDialog,
    StandardDetailDialog,
    StandardImportDialog,
    NewStandardDialog: () => import('./NewStandardDialog.vue'),
  },
  
  data() {
    return {
      tableData: [],
      loading: false,
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      // 新的筛选字段
      selectedWorkElement: '',
      searchKeyword: '',
      selectedStatus: '',
      workElements: [], // 工作单元列表
      selectedWorkUnit: [], // 级联选择器选中的工作单元
      workUnitOptions: [], // 级联选择器选项
      cascaderProps: {
        lazy: true,
        checkStrictly: true, // 允许选择任意级别
        emitPath: false, // 只返回最后一级的值
        value: 'value',
        label: 'label',
        children: 'children',
        leaf: 'isLeaf'
      },
      statistics: {
        configured: 0,
        unconfigured: 0,
        noNeed: 0
      },
      // 新增数据字段
      millConstructions: [],
      productList: [],
      EnterpriseID: '',

      // 配发标准导入对话框
      standardImportDialogVisible: false
    };
  },

  computed: {
    cascaderPropsComputed() {
      return {
        ...this.cascaderProps,
        lazyLoad: this.loadCascaderData
      };
    }
  },

  async created() {
    this.loadData();
    this.loadInitialData();
    this.initCascaderData();
  },
  
  methods: {
    // 将分类树转换为productList格式，支持多级级联选择器（自适应层级），参照ConfigDialog.vue
    transformCategoryTreeToProductList(categoryTree) {
      const productList = [];

      // 递归处理分类树，构建多级级联结构
      const processCategory = (category, parentPath = '') => {
        const currentPath = parentPath ? `${parentPath}/${category.name}` : category.name;

        // 如果有子分类，创建中间节点
        if (category.children && category.children.length > 0) {
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          // 递归处理所有子分类
          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          // 只有当有子数据时才添加到结果中
          if (categoryNode.data.length > 0) {
            return categoryNode;
          }
        } else {
          // 叶子节点，直接返回产品信息
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理子分类的辅助方法
      this.processChildCategory = (category, parentPath) => {
        const currentPath = `${parentPath}/${category.name}`;

        if (category.children && category.children.length > 0) {
          // 有子分类，创建中间节点
          const categoryNode = {
            product: category.name,
            type: 'category',
            data: []
          };

          category.children.forEach(child => {
            const childResult = this.processChildCategory(child, currentPath);
            if (childResult) {
              categoryNode.data.push(childResult);
            }
          });

          return categoryNode.data.length > 0 ? categoryNode : null;
        } else {
          // 叶子节点
          return {
            product: category.name,
            categoryId: category._id,
            categoryPath: currentPath,
            categoryName: category.name
          };
        }
      };

      // 处理顶级分类
      categoryTree.forEach(category => {
        const result = processCategory(category);
        if (result) {
          productList.push(result);
        }
      });

      return productList;
    },

    // 加载初始数据
    async loadInitialData() {
      try {
        // 加载组织架构数据
        const millRes = await findMillConstruction();
        if (millRes.status === 200) {
          this.millConstructions = millRes.data || [];
          if (this.millConstructions.length > 0) {
            this.EnterpriseID = this.millConstructions[0].EnterpriseID;
          }
        }

        // 加载防护用品分类数据
        const categoryRes = await getProtectionCategoryTree({
          includeSystem: true,
          activeOnly: true
        });
        if (categoryRes.status === 200) {
          this.productList = this.transformCategoryTreeToProductList(categoryRes.data || []);
        }
      } catch (error) {
        console.error('加载初始数据失败:', error);
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        const params = {
          filters: this.buildFilters(),
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize
        };

        console.log('发送请求参数:', params);
        const res = await getFlatStandardsList(params);
        console.log('接收到响应:', res);

        if (res && res.status === 200) {
          this.tableData = res.data.list || [];
          this.pagination.total = res.data.total || 0;
          this.pagination.currentPage = res.data.currentPage || 1;
          this.pagination.pageSize = res.data.pageSize || 20;

          // 计算统计信息
          this.calculateStatistics();
          // 提取工作单元列表
          this.extractWorkElements();
        } else {
          console.error('响应状态异常:', res);
          this.$message.error('获取数据失败，请检查网络连接');
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error(`加载数据失败: ${error.message || '未知错误'}`);
      } finally {
        this.loading = false;
      }
    },
    
    // 计算统计信息
    calculateStatistics() {
      this.statistics = {
        configured: this.tableData.filter(item => item.configStatus === 'configured').length,
        unconfigured: this.tableData.filter(item => item.configStatus === 'unconfigured').length,
        noNeed: this.tableData.filter(item => item.configStatus === 'no_need').length
      };
    },

    // 构建筛选条件
    buildFilters() {
      const filters = {};

      console.log('构建筛选条件时的状态:', {
        selectedWorkUnit: this.selectedWorkUnit,
        selectedWorkElement: this.selectedWorkElement,
        searchKeyword: this.searchKeyword
      });

      // 处理级联选择器的筛选 - 使用 fullId 进行层级筛选
      let workUnitValue = null;

      if (this.selectedWorkUnit) {
        if (Array.isArray(this.selectedWorkUnit) && this.selectedWorkUnit.length > 0) {
          // 如果是数组，取最后一个值
          workUnitValue = this.selectedWorkUnit[this.selectedWorkUnit.length - 1];
        } else if (typeof this.selectedWorkUnit === 'string') {
          // 如果是字符串，直接使用
          workUnitValue = this.selectedWorkUnit;
        }
      }

      // 使用 fullId 进行筛选，支持层级查询
      if (workUnitValue) {
        filters.fullId = workUnitValue;
        console.log('从级联选择器设置筛选条件 fullId:', workUnitValue);
      }

      // 兼容旧的工作单元筛选
      if (!filters.fullId && this.selectedWorkElement) {
        filters.fullId = this.selectedWorkElement;
        console.log('从兼容字段设置 fullId:', this.selectedWorkElement);
      }

      if (this.searchKeyword) {
        filters.keyword = this.searchKeyword;
      }
      if (this.selectedStatus) {
        filters.configStatus = this.selectedStatus;
      }
      return filters;
    },

    // 提取工作单元列表
    extractWorkElements() {
      const elements = new Set();
      this.tableData.forEach(item => {
        if (item.workshop) {
          elements.add(item.workshop);
        }
      });
      this.workElements = Array.from(elements);
    },

    // 查询按钮点击事件
    handleSearch() {
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 下载模板
    handleDownloadTemplate() {
      this.$message.info('下载模板功能开发中...');
    },

    // 导入标准
    handleImportStandards() {
      this.standardImportDialogVisible = true;
    },

    // 新增标准
    handleAddStandards() {
      // 打开新增标准弹窗，参照protectionPlan.vue中editNewForm的调用方式
      this.$refs.newStandardDialog.showModal({
        formData: null,
        millConstructions: this.millConstructions,
        productList: this.productList,
        grantType: 'mill', // 或者根据实际需要设置
        EnterpriseID: this.EnterpriseID,
      });
    },

    // 处理配发标准导入成功
    async handleImportSuccess() {
      this.$message.success('配发标准导入成功');
      // 刷新数据
      this.pagination.currentPage = 1;
      await this.loadData();
    },
    

    
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    
    // 处理排序变化
    handleSortChange({ column, prop, order }) {
      // 实现排序逻辑
      console.log('排序变化:', { column, prop, order });
    },
    
    // 处理配置状态变化
    async handleStatusChange(row) {
      try {
        // 如果从无需配置改为其他状态，需要重新计算实际状态
        if (row.configStatus !== 'no_need') {
          this.updateConfigStatus(row);
        }

        await updateConfigStatus({
          workStationIds: [row.fullId], // 使用 fullId 而不是 _id
          configStatus: row.configStatus
        });
        this.$message.success('配置状态更新成功');
        this.calculateStatistics();
        // 重新加载数据以获取最新状态
        this.loadData();
      } catch (error) {
        console.error('更新配置状态失败:', error);
        this.$message.error('更新配置状态失败');
      }
    },

    // 处理状态命令（下拉菜单）
    handleStatusCommand(command, row) {
      if (command === 'no_need') {
        this.$confirm('确认将此工种设置为无需配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          row.configStatus = 'no_need';
          this.handleStatusChange(row);
          this.$message.success('已设置为无需配置');
        }).catch(() => {
          // 用户取消
        });
      }
    },

    // 更新配置状态（自动判断）
    updateConfigStatus(row) {
      // 基于是否有防护用品配置来自动判断状态
      if (row.protectionPlans && row.protectionPlans.length > 0) {
        row.configStatus = 'configured';
      } else {
        row.configStatus = 'unconfigured';
      }
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        'configured': 'el-icon-check',
        'unconfigured': 'el-icon-warning',
        'no_need': 'el-icon-minus'
      };
      return iconMap[status] || 'el-icon-question';
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'configured': 'success',
        'unconfigured': 'warning',
        'no_need': 'info'
      };
      return typeMap[status] || '';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'configured': '已配置',
        'unconfigured': '未配置',
        'no_need': '无需配置'
      };
      return textMap[status] || '未知';
    },

    // 初始化级联选择器数据
    async initCascaderData() {
      try {
        // 加载企业级别的数据
        const response = await this.$http.get('/api/workunit/cascade', {
          params: {
            level: 0,
            pageSize: 100
          }
        });

        console.log('初始化级联数据响应:', response);

        if (response.data && response.data.code === 200) {
          this.workUnitOptions = response.data.data;
          console.log('设置workUnitOptions:', this.workUnitOptions);
        } else {
          console.error('初始化级联数据失败，响应:', response);
        }
      } catch (error) {
        console.error('初始化级联数据失败:', error);
      }
    },

    // 级联选择器懒加载数据
    async loadCascaderData(node, resolve) {
      const { level, value } = node;

      console.log('懒加载调试信息:', {
        level,
        value,
        node: node,
        data: node.data,
        '原始level': level,
        '修正后的level': level
      });

      try {
        // 修正 level 计算：前端级联选择器的 level 1 对应后端的 level 1（厂房）
        const response = await this.$http.get('/api/workunit/cascade', {
          params: {
            level: level, // 不再 +1，直接使用 level
            parentId: value,
            pageSize: 100
          }
        });

        if (response.data && response.data.code === 200) {
          resolve(response.data.data);
        } else {
          resolve([]);
        }
      } catch (error) {
        console.error('加载级联数据失败:', error);
        resolve([]);
      }
    },

    // 处理工作单元变更
    handleWorkUnitChange(value) {
      console.log('工作单元变更:', value);

      // 处理级联选择器返回的值
      let selectedValue = value;
      if (Array.isArray(value) && value.length > 0) {
        selectedValue = value[value.length - 1]; // 取最后一级的值
      }

      this.selectedWorkElement = selectedValue; // 保持兼容性
      console.log('处理后的选中值:', selectedValue);
      this.loadData(); // 重新加载数据
    },

    // 处理工作单元变更（保留兼容）
    handleWorkElementChange(value) {
      this.handleWorkUnitChange(value);
    },
    
    // 批量配置
    handleBatchConfig() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要配置的工种');
        return;
      }
      // 实现批量配置逻辑
      console.log('批量配置:', this.selectedRows);
    },
    
    // 配置单个工种
    async handleConfig(row) {
      console.log(row,'row');
      console.log('行数据中的existingPlanId:', row.existingPlanId);

      try {
        // 直接使用 saveSingle 接口的查询逻辑，通过传递 _id 来查询现有数据
        // 先尝试查询是否已有配发标准
        // 使用专门的接口查询该工种的配发标准详情
        console.log('查询工种详情，fullId:', row.fullId);
        const response = await this.$http.post('/api/defendproducts/getStationDetail', {
          fullId: row.fullId
        });

        console.log('查询到的工种详情:', response.data);

        // 从返回的数据中提取防护用品配置
        let protectionPlans = row.protectionPlans || [];
        let existingPlanId = null;

        if (response.data && response.data.data) {
          const stationDetail = response.data.data;

          // 提取现有计划ID
          existingPlanId = stationDetail.existingPlanId;

          // 直接使用接口返回的protectionPlans数组
          if (stationDetail.protectionPlans && stationDetail.protectionPlans.length > 0) {
            protectionPlans = stationDetail.protectionPlans.map(plan => ({
              ...plan,
              // 确保必要字段存在
              productType: plan.productType || [],
              time: plan.time || '',
              timeUnit: plan.timeUnit || 'M',
              number: plan.number || 1,
              maxNumber: plan.maxNumber || '',
              categoryId: plan.categoryId || '',
              categoryPath: plan.categoryPath || '',
              categoryName: plan.categoryName || plan.product || '',
              remark: plan.remark || ''
            }));
            console.log('提取到的防护用品配置:', protectionPlans);
          }
        }

        // 准备传递给配置弹窗的数据
        const workStationWithData = {
          // 使用从接口返回的完整工种信息，确保字段一致
          ...(response.data && response.data.data ? response.data.data : row),
          productList: this.productList,
          millConstructions: this.millConstructions,
          // 添加已保存的防护用品配置（深拷贝确保数据独立性）
          protectionPlans: JSON.parse(JSON.stringify(protectionPlans)),
          // 添加现有计划ID，用于更新
          existingPlanId: existingPlanId
        };

        console.log('NewProtectionPlan - 调试信息:');
        console.log('  - response.data.data:', response.data && response.data.data ? response.data.data : null);
        console.log('  - row:', row);
        console.log('  - existingPlanId变量:', existingPlanId);
        console.log('  - workStationWithData.existingPlanId:', workStationWithData.existingPlanId);
        console.log('  - protectionPlans数量:', workStationWithData.protectionPlans ? workStationWithData.protectionPlans.length : 0);

        this.$refs.configDialog.open(workStationWithData);
      } catch (error) {
        console.error('查询配发标准失败:', error);
        // 即使查询失败，也要打开配置弹窗，只是没有历史数据
        // 但是需要尝试从row数据中获取existingPlanId
        const workStationWithData = {
          // 深拷贝行数据确保数据独立性
          ...JSON.parse(JSON.stringify(row)),
          productList: this.productList,
          millConstructions: this.millConstructions,
          protectionPlans: [],
          // 修复Bug：即使查询失败，也要尝试传递existingPlanId
          existingPlanId: row.existingPlanId || null
        };
        console.log('查询失败时的workStationWithData:', workStationWithData);
        this.$refs.configDialog.open(workStationWithData);
      }
    },

    // 显示员工列表
    showEmployees(row) {
      console.log('显示员工列表:', row);
      if (row.employeeCount === 0) {
        this.$message.info('该工种暂无员工');
        return;
      }
      this.$refs.employeeDialog.open(row);
    },
    
    // 显示标准详情
    showStandardDetail(row) {
      console.log('显示标准详情:', row);
      this.$refs.standardDialog.open(row);
    },



    // 处理新增标准保存
    handleNewStandardSave(data) {
      // 刷新数据列表
      this.loadData();
      this.$message.success('新增标准成功');
    },
    
    // 获取标准摘要
    getStandardSummary(plan) {
      if (plan.products && plan.products.length > 0) {
        const product = plan.products[0];
        return `${product.product || '防护用品'} x${product.number || 1}`;
      }
      return '防护用品';
    },

    // 获取标准详情
    getStandardDetail(plan) {
      // 处理新的数据结构：plan 本身就是一个防护用品配置
      if (plan.product) {
        const timeUnit = plan.timeUnit === 'M' ? '月' : plan.timeUnit === 'Y' ? '年' : plan.timeUnit === 'D' ? '天' : '';
        const cycle = plan.time && timeUnit ? `/${plan.time}${timeUnit}` : '';
        return `${plan.product} x${plan.number || 1}${cycle}`;
      }

      // 兼容旧的数据结构
      if (plan.products && plan.products.length > 0) {
        const details = plan.products.map(product => {
          const cycle = product.cycle ? `/${product.cycle}` : '';
          return `${product.product || '防护用品'} x${product.number || 1}${cycle}`;
        });
        return details.join('、');
      }

      return '暂无详情';
    },

    // 获取显示的分类名称
    getDisplayCategoryName(plan) {
      // 优先使用categoryName
      if (plan.categoryName) {
        return plan.categoryName;
      }

      // 如果有categoryPath，从路径中提取最后一级
      if (plan.categoryPath) {
        const pathParts = plan.categoryPath.split('/');
        return pathParts[pathParts.length - 1];
      }

      // 兼容旧数据：使用productType的最后一级
      if (plan.productType && plan.productType.length > 0) {
        return plan.productType[plan.productType.length - 1];
      }

      // 最后使用product字段
      return plan.product || '防护用品';
    },

    // 获取分类汇总信息
    getCategorySummary(protectionPlans) {
      const categoryMap = new Map();

      protectionPlans.forEach(plan => {
        // 获取大类名称（父分类）
        let categoryName = '';

        if (plan.categoryPath) {
          const pathParts = plan.categoryPath.split('/');
          categoryName = pathParts[0]; // 取第一级作为大类
        } else if (plan.productType && plan.productType.length > 0) {
          categoryName = plan.productType[0]; // 取第一级作为大类
        } else {
          categoryName = '其他防护';
        }

        // 统计数量
        if (categoryMap.has(categoryName)) {
          categoryMap.set(categoryName, categoryMap.get(categoryName) + (plan.number || 1));
        } else {
          categoryMap.set(categoryName, plan.number || 1);
        }
      });

      // 转换为数组并排序
      return Array.from(categoryMap.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count); // 按数量降序排列
    },

    // 获取标签类型
    getTagType(category) {
      const typeMap = {
        // 大类
        '呼吸防护': 'success',
        '听力防护': 'warning',
        '眼面防护': 'info',
        '头部防护': 'primary',
        '手部防护': '',
        '足部防护': 'success',
        '身体防护': 'warning',
        // 具体防护用品
        '安全帽': 'primary',
        '防尘口罩': 'success',
        '防毒面具': 'success',
        '耳塞': 'warning',
        '耳罩': 'warning',
        '防护眼镜': 'info',
        '面罩': 'info'
      };
      return typeMap[category] || '';
    },


    
    // 导出数据
    handleExport() {
      this.$message.info('导出功能开发中...');
    },
    
    // 刷新数据
    refreshData() {
      this.loadData();
    },
    
    // 处理页面大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadData();
    },
    
    // 处理当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadData();
    },
    
    // 处理配置保存
    async handleConfigSave(data) {
      console.log('保存配置:', data);

      // 保存成功后重新获取服务端最新数据，确保数据一致性
      try {
        await this.loadData();
        this.$message.success('配置保存成功');
      } catch (error) {
        console.error('重新加载数据失败:', error);
        this.$message.error('保存成功，但刷新数据失败，请手动刷新页面');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.new-protection-plan {
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;

      .el-select, .el-input {
        margin-right: 10px;
      }
    }

    .right-actions {
      display: flex;
      align-items: center;

      .el-button {
        margin-left: 8px;
      }
    }
  }

  // 防护用品标准单元格样式
  .protection-standards-cell {
    .standards-list {
      .standard-item {
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        .standard-detail {
          font-size: 12px;
          color: #606266;
          flex: 1;
        }
      }
    }

    .no-config {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0;
    }
  }

  // 表格样式优化
  ::v-deep .el-table {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #F5F7FA !important;
          color: #4C91E9 !important;
          font-weight: normal !important;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: #F5F7FA;
        }
      }
    }

    // 配置状态选择器样式
    .el-select {
      .el-input__inner {
        border: none;
        background: transparent;
        text-align: center;
      }

      &:hover .el-input__inner {
        border: 1px solid #C0C4CC;
        background: #fff;
      }
    }
  }

  // 人员数量徽章样式
  ::v-deep .el-badge {
    .el-badge__content {
      background-color: #409EFF;
      border: none;
    }
  }

  // 操作按钮样式
  .el-button {
    &.is-disabled {
      opacity: 0.5;
    }
  }
}
</style>

<style lang="scss" scoped>
.new-protection-plan {
  .action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .el-tag {
    margin-right: 5px;
    margin-bottom: 5px;
    font-weight: 500;
  }

  .item {
    margin-right: 18px;
  }

  // 防护用品标准配置展示样式
  .protection-standards-cell {
    padding: 8px 0;
  }

  .standards-summary {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .category-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
  }



  .no-config {
    display: flex;
    align-items: center;
    color: #999;
    font-size: 14px;
  }
}
</style>
